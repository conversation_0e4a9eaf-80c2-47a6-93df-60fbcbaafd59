import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DashboardDemo, DemoLauncher } from '~/components/lighthouse';
import { useLighthouseStore } from '~/components/lighthouse';

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock the lighthouse store
vi.mock('~/components/lighthouse', async () => {
  const actual = await vi.importActual('~/components/lighthouse');
  return {
    ...actual,
    useLighthouseStore: vi.fn(),
  };
});

// Mock accessibility utils
vi.mock('~/utils/accessibilityUtils', () => ({
  AccessibilityUtils: {
    prefersReducedMotion: () => true, // Always return true for tests
  },
}));

describe('Lighthouse Dashboard Demo', () => {
  const mockStore = {
    currentProject: {
      id: 'test-project',
      name: 'Test Project',
      description: 'Test Description',
      domain: 'Test',
      status: 'planning',
      created: new Date(),
      updated: new Date(),
      intelligence: {
        contextId: 'test-context',
        knowledgeGraphId: 'test-kg',
        learningLevel: 0,
        keyInsights: [],
        activePatterns: [],
        domainExpertise: {
          primaryDomain: 'Test',
          relatedDomains: [],
          concepts: [],
          vocabulary: [],
          expertiseLevel: 0,
        },
      },
    },
    projectContext: {
      project: {} as any,
      activeKnowledge: [],
      recentInsights: [],
      runningAgents: [],
    },
    insights: [],
    activeAgents: [],
    knowledgeSources: [],
    learningEvents: [],
    navigateToModule: vi.fn(),
    initializeSession: vi.fn(),
    addInsight: vi.fn(),
    recordLearning: vi.fn(),
    deployAgent: vi.fn(),
    updateAgentStatus: vi.fn(),
    addSuggestion: vi.fn(),
  };

  beforeEach(() => {
    vi.mocked(useLighthouseStore).mockReturnValue(mockStore);
  });

  describe('DemoLauncher', () => {
    it('renders the demo launcher with both demo options', () => {
      render(<DemoLauncher />);
      
      expect(screen.getByText('Lighthouse Demos')).toBeInTheDocument();
      expect(screen.getByText('Dashboard Live Demo')).toBeInTheDocument();
      expect(screen.getByText('Enhanced UI Components')).toBeInTheDocument();
      expect(screen.getByText('Launch Dashboard Demo')).toBeInTheDocument();
      expect(screen.getByText('Explore UI Components')).toBeInTheDocument();
    });

    it('launches dashboard demo when button is clicked', () => {
      render(<DemoLauncher />);
      
      const dashboardButton = screen.getByText('Launch Dashboard Demo');
      fireEvent.click(dashboardButton);
      
      // Should show the dashboard demo
      expect(screen.getByText('Dashboard Live Demo')).toBeInTheDocument();
      expect(screen.getByText('← Back to Demos')).toBeInTheDocument();
    });

    it('launches UI demo when button is clicked', () => {
      render(<DemoLauncher />);
      
      const uiButton = screen.getByText('Explore UI Components');
      fireEvent.click(uiButton);
      
      // Should show the UI demo
      expect(screen.getByText('← Back to Demos')).toBeInTheDocument();
    });

    it('returns to launcher when back button is clicked', () => {
      render(<DemoLauncher />);
      
      // Launch dashboard demo
      const dashboardButton = screen.getByText('Launch Dashboard Demo');
      fireEvent.click(dashboardButton);
      
      // Click back button
      const backButton = screen.getByText('← Back to Demos');
      fireEvent.click(backButton);
      
      // Should be back to launcher
      expect(screen.getByText('Lighthouse Demos')).toBeInTheDocument();
    });
  });

  describe('DashboardDemo', () => {
    it('renders the dashboard demo with controls', () => {
      render(<DashboardDemo />);
      
      expect(screen.getByText('Dashboard Live Demo')).toBeInTheDocument();
      expect(screen.getByText('Interactive Preview')).toBeInTheDocument();
      expect(screen.getByText('Live Demo:')).toBeInTheDocument();
      expect(screen.getByText('Reset')).toBeInTheDocument();
    });

    it('shows demo progress indicators', () => {
      render(<DashboardDemo />);
      
      expect(screen.getByText('Demo Progress:')).toBeInTheDocument();
      expect(screen.getByText(/Step \d+ of \d+/)).toBeInTheDocument();
    });

    it('has tabs for different views', () => {
      render(<DashboardDemo />);
      
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Controls')).toBeInTheDocument();
      expect(screen.getByText('Info')).toBeInTheDocument();
    });

    it('toggles live demo mode', async () => {
      render(<DashboardDemo />);
      
      // Find the switch (it might be a checkbox input)
      const switches = screen.getAllByRole('switch');
      expect(switches.length).toBeGreaterThan(0);
      
      // Toggle the first switch (should be the live demo toggle)
      fireEvent.click(switches[0]);
      
      // The demo should now be in live mode
      // We can't easily test the interval behavior in unit tests,
      // but we can verify the UI responds to the toggle
    });

    it('resets demo when reset button is clicked', () => {
      render(<DashboardDemo />);
      
      const resetButton = screen.getByText('Reset');
      fireEvent.click(resetButton);
      
      expect(mockStore.initializeSession).toHaveBeenCalled();
    });

    it('shows controls tab content', () => {
      render(<DashboardDemo />);
      
      const controlsTab = screen.getByText('Controls');
      fireEvent.click(controlsTab);
      
      expect(screen.getByText('Demo Controls')).toBeInTheDocument();
      expect(screen.getByText('Manual Steps')).toBeInTheDocument();
      expect(screen.getByText('Demo Speed')).toBeInTheDocument();
    });

    it('shows info tab content', () => {
      render(<DashboardDemo />);
      
      const infoTab = screen.getByText('Info');
      fireEvent.click(infoTab);
      
      expect(screen.getByText('About This Demo')).toBeInTheDocument();
      expect(screen.getByText('Features Demonstrated:')).toBeInTheDocument();
      expect(screen.getByText('Technologies Used:')).toBeInTheDocument();
    });
  });

  describe('Demo Integration', () => {
    it('calls store methods when demo steps are executed', async () => {
      render(<DashboardDemo />);
      
      // Switch to controls tab
      const controlsTab = screen.getByText('Controls');
      fireEvent.click(controlsTab);
      
      // Find and click a manual step button
      const stepButtons = screen.getAllByText(/\d+\./);
      if (stepButtons.length > 1) {
        fireEvent.click(stepButtons[1]); // Click second step
        
        // Verify that store methods are called
        await waitFor(() => {
          expect(mockStore.recordLearning).toHaveBeenCalled();
        });
      }
    });

    it('handles demo speed changes', () => {
      render(<DashboardDemo />);
      
      // Switch to controls tab
      const controlsTab = screen.getByText('Controls');
      fireEvent.click(controlsTab);
      
      // Find speed control buttons
      const slowButton = screen.getByText('Slow (4s)');
      const normalButton = screen.getByText('Normal (2s)');
      const fastButton = screen.getByText('Fast (1s)');
      
      expect(slowButton).toBeInTheDocument();
      expect(normalButton).toBeInTheDocument();
      expect(fastButton).toBeInTheDocument();
      
      // Click different speed buttons
      fireEvent.click(slowButton);
      fireEvent.click(fastButton);
      fireEvent.click(normalButton);
    });
  });

  describe('Accessibility', () => {
    it('respects reduced motion preferences', () => {
      render(<DashboardDemo />);
      
      // The component should render without throwing errors
      // when reduced motion is enabled (which we mocked to always return true)
      expect(screen.getByText('Dashboard Live Demo')).toBeInTheDocument();
    });

    it('has proper ARIA labels and roles', () => {
      render(<DemoLauncher />);
      
      // Check for proper heading structure
      const mainHeading = screen.getByText('Lighthouse Demos');
      expect(mainHeading).toBeInTheDocument();
      
      // Check for buttons with proper text
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
    });
  });
});
